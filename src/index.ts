// Export the main CropLibrary class
export { CropLibrary } from './core/CropLibrary';

// Export the main DiseaseLibrary class
export { DiseaseLibrary } from './core/DiseaseLibrary';

// Export types
export {
  AgriItem,
  AgriItemType,
  AgriPlant,
  CropFamily,
  CropLibraryConfig,
  CropLibraryOptions,
  DateType,
  Disease,
  DiseaseType,
  DiseaseLibraryConfig,
  DiseaseLibraryOptions,
  ImageData,
  LangMapItem,
  LangText,
  MeasurementUnit,
  ObjectId,
  SummarySection
} from './types';

// Export utility classes
export { CacheManager } from './utils/cache';
export { ImageUtils } from './utils/images';

// Export crop image utilities
export {
  getCropImage,
  getAvailableCropNames,
  hasCropImage
} from './utils/cropImages';

// Export disease image utilities
export {
  getDiseaseImage,
  // getDiseaseImageById,
  getDiseaseTypeImage,
  // getDiseaseTypeImageById,
  getAvailableDiseaseNames,
  getAvailableDiseaseTypeNames,
  hasDiseaseImage,
  hasDiseaseTypeImage,
  // getAllDiseaseImageIds,
  // getAllDiseaseTypeImageIds
} from './utils/diseaseImages';

// Export raw data for direct access
export {
  cropFamilies,
  getAllCropFamilies,
  getCropFamilyById
} from './data/cropFamilies';

export {
  plants,
  getAllPlants,
  getPlantById,
  getPlantsByCropFamily,
  searchPlants
} from './data/plants';

export {
  diseaseTypes,
  getAllDiseaseTypes,
  getDiseaseTypeById,
  searchDiseaseTypes
} from './data/diseaseTypes';

export {
  diseases,
  getAllDiseases,
  getDiseaseById,
  getDiseasesByType,
  getDiseasesByTypeAndLanguage,
  searchDiseases
} from './data/diseases';

export {
  imagePathMap,
  getImageById,
  getImageUrl,
  getAllImageIds
} from './data/images';