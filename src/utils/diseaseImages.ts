// Direct disease image mapping using require()
const imagePlaceholder = require('../../../assets/img/smallImage.png');

// Map disease names to their require() statements
const diseaseImageMap: Record<string, any> = {
  // Disease images based on available PNG files
  'COCOA_BLACK_POD': require('../data/images/AGRI_DATA/DISEASES/PNG/Cocoa black pod.png'),
  'ORANGE_CITRUS_GREENING': require('../data/images/AGRI_DATA/DISEASES/PNG/Orange citrus greening.png'),
  'SORGHUM_DOWNY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/Sorghum downy mildew.png'),
  'CASSAVA_MOSAIC_VIRUS': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava Mosaic Virus.png'),
  'KALE_BLACK_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/Kale black rot.png'),
  'ONION_NECK_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/Onion neck rot.png'),
  'YAM_ANTHRACNOSE': require('../data/images/AGRI_DATA/DISEASES/PNG/Yam anthracnose.png'),
  'BANANA_BLACK_SIGATOKA': require('../data/images/AGRI_DATA/DISEASES/PNG/Banana black sigatoka.png'),
  'CASSAVA_LEAF_SPOT': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava Leaf Spot.png'),
  'CHIVE_RUST': require('../data/images/AGRI_DATA/DISEASES/PNG/Chive rust.png'),
  'CLOVER_POWDERY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/Clover powdery mildew.png'),
  'COFFEE_RUST': require('../data/images/AGRI_DATA/DISEASES/PNG/Coffee Rust.png'),
  'COWPEA_BACTERIAL_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/Cowpea bacterial blight.png'),
  'LEMON_CITRUS_CANKER': require('../data/images/AGRI_DATA/DISEASES/PNG/Lemon citrus canker.png'),
  'MAIZE_STREAK_VIRUS': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize streak virus.png'),
  'MANGO_BACTERIAL_BLACK_SPOT': require('../data/images/AGRI_DATA/DISEASES/PNG/Mango bacterial black spot.png'),
  'OAT_SMUT': require('../data/images/AGRI_DATA/DISEASES/PNG/Oat smut.png'),
  'CASSAVA_BACTERIAL_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava Bacterial blight.png'),
  'CELERY_FUSARIUM': require('../data/images/AGRI_DATA/DISEASES/PNG/Celery fusarium.png'),
  'GRAPE_POWDERY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/Grape powdery mildew.png'),
  'MAIZE_STALK_AND_EAR_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize stalk and ear rot.png'),
  'MARIGOLD_BOTRYTIS': require('../data/images/AGRI_DATA/DISEASES/PNG/Marigold Botrytis.png'),
  'CAULIFLOWER_BLACK_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/cauliflower black rot.png'),
  'IRISH_POTATO_BACTERIAL_WILT': require('../data/images/AGRI_DATA/DISEASES/PNG/Irish Potato bacterial wilt.png'),
  'MAIZE_COMMON_SMUT': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize common smut.png'),
  'MILLET_RUST': require('../data/images/AGRI_DATA/DISEASES/PNG/Millet rust.png'),
  'PINEAPPLE_BACTERIAL_HEART_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/Pineapple bacterial heart rot.png'),
  'TOMATO_LATE_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/Tomato late blight.png'),
  'TOBACCO_BLACK_SHANK': require('../data/images/AGRI_DATA/DISEASES/PNG/Tobacco black shank.png'),
  'POTATO_EARLY_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/Potato early blight.png'),
  'PARSNIP_POWDERY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/Parsnip powdery mildew.png'),
  'CHINESE_CABBAGE_CLUBROOT': require('../data/images/AGRI_DATA/DISEASES/PNG/Chinese cabbage clubroot.png'),
  'GRAPE_DOWNY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/Grape downy mildew.png'),
  'OIL_PALM_GANODERMA_BASAL_STEM_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/Oil Palm ganoderma basal stem rot.png'),
  
  // Additional disease mappings with normalized names
  'BEANS_BACTERIAL_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/beans_bacterial Blight_colour ground.png'),
  'APPLE_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/apple_general_fungus_card.png'),
  'BROCCOLI_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/broccoli_general_fungus_card.png'),
  'CABBAGE_BLACK_ROT': require('../data/images/AGRI_DATA/DISEASES/PNG/cabbage_black rot_coloured ground.png'),
  'APPLE_FIRE_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/apple_fire blight_coloured ground.png'),
  'CUCURBIT_BACTERIAL_WILT': require('../data/images/AGRI_DATA/DISEASES/PNG/curcubit_bacterial wilt_colour ground.png'),
  'BOK_CHOI_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/bok choi_general_fungus_card.png'),
  'CUCURBIT_DOWNY_MILDEW': require('../data/images/AGRI_DATA/DISEASES/PNG/curcubitt_downy mildew_coloured ground.png'),
  'BROAD_BEAN_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/broad bean_general_fungus_card.png'),
  'COWPEA_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/cowpea_general_fungus_card.png'),
  'GARLIC_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/garlic_general_fungus_card.png'),
  'GINGER_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/ginger_general_fungus_card.png'),
  'IVY_GOURD_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/ivy gourd_general_fungus_card.png'),
  'MELON_BACTERIAL_BLIGHT': require('../data/images/AGRI_DATA/DISEASES/PNG/melon_bacterial blight_coloured ground.png'),
  'PEPPER_CHILI_BACTERIAL_WILT': require('../data/images/AGRI_DATA/DISEASES/PNG/pepper_chili_bacterial wilt_coloured ground.png'),
  'VIRUS_CARD_PLACEHOLDER': require('../data/images/AGRI_DATA/DISEASES/PNG/Virus_ card_placeholder.png'),
  'CASSAVA_LEAF_SPOT_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava_leaf spot_coloured ground.png'),
  'CASSAVA_MOSAIC_VIRUS_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava_mosaic virus_coloured ground.png'),
  'ALFALFA_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/alfalfa_general_fungus_card.png'),
  'CHIVE_RUST_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Chive_rust_coloured ground.png'),
  'CLOVER_POWDERY_MILDEW_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Clover_powdery mildew_coloured ground.png'),
  'CORN_DOWNY_MILDEW_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Corn_Downy mildew_coloured ground.png'),
  'GARLIC_WHITE_BULB_ROT_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Garlic_white bulb rot_coloured ground.png'),
  'MAIZE_STREAK_VIRUS_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize_streak virus.png'),
  'CABBAGE_GENERAL_FUNGUS': require('../data/images/AGRI_DATA/DISEASES/PNG/cabbage_general_fungus_card.png'),
  'TURNIP_CLUBROOT': require('../data/images/AGRI_DATA/DISEASES/PNG/turnip clubroot.png'),
  'CHINESE_CABBAGE_CLUBROOT_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Chinese cabbage_clubroot_colour ground rot_colour ground.png'),
  'YAM_MOSAIC_VIRUS_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/YAM_MOSAIC VIRUS_coloured ground.png'),
  'IRISH_POTATO_BACTERIAL_WILT_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Irish potato_bacterial wilt_colour ground.png'),
  'CORN_COMMON_RUST_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Corn_common rust_coloured ground.png'),
  'BANANA_PANAMA_DISEASE_COLOURED': require('../data/images/AGRI_DATA/DISEASES/PNG/Banana_panama disease_coloured ground.png'),
  
  // Alternative spellings and variations
  'ANTHRACNOSE_GUINEA_CORN': require('../data/images/AGRI_DATA/DISEASES/PNG/Yam anthracnose.png'),
  'BACTERIAL_BLIGHT_CASSAVA': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava Bacterial blight.png'),
  'BACTERIAL_BLIGHT_MELON': require('../data/images/AGRI_DATA/DISEASES/PNG/melon_bacterial blight_coloured ground.png'),
  'BACTERIAL_BLIGHT_COWPEA': require('../data/images/AGRI_DATA/DISEASES/PNG/Cowpea_bacterial blight_colour ground.png'),
  'BLACK_POD_DISEASE': require('../data/images/AGRI_DATA/DISEASES/PNG/Cocoa black pod.png'),
  'DOWNY_MILDEW_SORGHUM': require('../data/images/AGRI_DATA/DISEASES/PNG/Sorghum downy mildew.png'),
  'EARLY_BLIGHT_POTATO': require('../data/images/AGRI_DATA/DISEASES/PNG/Potato_early blight_coloured ground.png'),
  'EARLY_BLIGHT_TOMATO': require('../data/images/AGRI_DATA/DISEASES/PNG/Tomato_early blight_coloured ground.png'),
  'LATE_BLIGHT_TOMATO': require('../data/images/AGRI_DATA/DISEASES/PNG/Tomato_late blight_coloured ground.png'),
  'MOSAIC_VIRUS_CASSAVA': require('../data/images/AGRI_DATA/DISEASES/PNG/Cassava_mosaic virus_coloured ground.png'),
  'MOSAIC_VIRUS_YAM': require('../data/images/AGRI_DATA/DISEASES/PNG/YAM_MOSAIC VIRUS_coloured ground.png'),
  'POWDERY_MILDEW_CLOVER': require('../data/images/AGRI_DATA/DISEASES/PNG/Clover_powdery mildew_coloured ground.png'),
  'POWDERY_MILDEW_GRAPE': require('../data/images/AGRI_DATA/DISEASES/PNG/Grape_powdery mildew_coloured ground.png'),
  'RUST_CHIVE': require('../data/images/AGRI_DATA/DISEASES/PNG/Chive_rust_coloured ground.png'),
  'RUST_COFFEE': require('../data/images/AGRI_DATA/DISEASES/PNG/Coffee Rust.png'),
  'RUST_MILLET': require('../data/images/AGRI_DATA/DISEASES/PNG/Millet_rust_coloured ground.png'),
  'SMUT_MAIZE': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize_common smut_coloured ground.png'),
  'SMUT_OAT': require('../data/images/AGRI_DATA/DISEASES/PNG/Oat_smut_coloured ground.png'),
  'STREAK_VIRUS_MAIZE': require('../data/images/AGRI_DATA/DISEASES/PNG/Maize_streak virus_coloured ground.png'),
  'WILT_BACTERIAL_IRISH_POTATO': require('../data/images/AGRI_DATA/DISEASES/PNG/Irish potato_bacterial wilt_colour ground.png'),
};

// Map disease type names to their require() statements
const diseaseTypeImageMap: Record<string, any> = {
  'FUNGUS': require('../data/images/AGRI_DATA/DISEASE_TYPE/PNG/fungus.png'),
  'BACTERIAL': require('../data/images/AGRI_DATA/DISEASE_TYPE/PNG/bacterial.png'),
  'VIRUS': require('../data/images/AGRI_DATA/DISEASE_TYPE/PNG/virus.png'),
  'OOMYCETES': require('../data/images/AGRI_DATA/DISEASE_TYPE/PNG/oomycetes.png'),
  'PROTIST': require('../data/images/AGRI_DATA/DISEASE_TYPE/PNG/protist.png'),
};

/**
 * Get disease image by disease name
 * @param diseaseName - The name of the disease
 * @returns The image source for the disease or a placeholder if not found
 */
export const getDiseaseImage = (diseaseName: string) => {
  // console.log(diseaseName);
  if (!diseaseName) return imagePlaceholder;
  
  // Normalize the disease name: uppercase and replace spaces with underscores
  const normalizedName = diseaseName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Try exact match first
  if (diseaseImageMap[normalizedName]) {
    return diseaseImageMap[normalizedName];
  }
  
  // Try partial matches with improved logic
  for (const [key, image] of Object.entries(diseaseImageMap)) {
    // Check if the normalized name contains the key or vice versa
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return image;
    }
    
    // Also try matching without underscores
    const nameWithoutUnderscores = normalizedName.replace(/_/g, '');
    const keyWithoutUnderscores = key.replace(/_/g, '');
    if (nameWithoutUnderscores.includes(keyWithoutUnderscores) || keyWithoutUnderscores.includes(nameWithoutUnderscores)) {
      return image;
    }
  }
  
  // Fallback to placeholder
  return imagePlaceholder;
};

/**
 * Get disease type image by disease type name
 * @param diseaseTypeName - The name of the disease type
 * @returns The image source for the disease type or a placeholder if not found
 */
export const getDiseaseTypeImage = (diseaseTypeName: string) => {
  if (!diseaseTypeName) return imagePlaceholder;
  
  // Normalize the disease type name: uppercase and replace spaces with underscores
  const normalizedName = diseaseTypeName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Try exact match first
  if (diseaseTypeImageMap[normalizedName]) {
    return diseaseTypeImageMap[normalizedName];
  }
  
  // Try partial matches
  for (const [key, image] of Object.entries(diseaseTypeImageMap)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return image;
    }
  }
  
  // Fallback to placeholder
  return imagePlaceholder;
};

/**
 * Get all available disease names that have images
 * @returns Array of disease names that have corresponding images
 */
export const getAvailableDiseaseNames = (): string[] => {
  return Object.keys(diseaseImageMap);
};

/**
 * Get all available disease type names that have images
 * @returns Array of disease type names that have corresponding images
 */
export const getAvailableDiseaseTypeNames = (): string[] => {
  return Object.keys(diseaseTypeImageMap);
};

/**
 * Check if a disease has an available image
 * @param diseaseName - The name of the disease to check
 * @returns True if the disease has an image, false otherwise
 */
export const hasDiseaseImage = (diseaseName: string): boolean => {
  if (!diseaseName) return false;
  
  // Normalize the disease name: uppercase and replace spaces with underscores
  const normalizedName = diseaseName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Check exact match
  if (diseaseImageMap[normalizedName]) {
    return true;
  }
  
  // Check partial matches with improved logic
  for (const key of Object.keys(diseaseImageMap)) {
    // Check if the normalized name contains the key or vice versa
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return true;
    }
    
    // Also try matching without underscores
    const nameWithoutUnderscores = normalizedName.replace(/_/g, '');
    const keyWithoutUnderscores = key.replace(/_/g, '');
    if (nameWithoutUnderscores.includes(keyWithoutUnderscores) || keyWithoutUnderscores.includes(nameWithoutUnderscores)) {
      return true;
    }
  }
  
  return false;
};

/**
 * Check if a disease type has an available image
 * @param diseaseTypeName - The name of the disease type to check
 * @returns True if the disease type has an image, false otherwise
 */
export const hasDiseaseTypeImage = (diseaseTypeName: string): boolean => {
  if (!diseaseTypeName) return false;
  
  // Normalize the disease type name: uppercase and replace spaces with underscores
  const normalizedName = diseaseTypeName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Check exact match
  if (diseaseTypeImageMap[normalizedName]) {
    return true;
  }
  
  // Check partial matches
  for (const key of Object.keys(diseaseTypeImageMap)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return true;
    }
  }
  
  return false;
};
